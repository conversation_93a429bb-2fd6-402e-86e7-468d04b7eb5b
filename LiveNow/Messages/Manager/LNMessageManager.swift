//
//  LNMessageManager.swift
//  LiveNow
//
//  Created by po on 2025/8/23.
//

import UIKit
import RongIMLibCore

class LNMessageManager: NSObject {
    static let shared = LNMessageManager()
    private override init() {}
    
    func configIMSDK() {
        let options = RCInitOption()
        //数据中心
        options.naviServer = kRongIMUrl
        //初始化IM
        RCCoreClient.shared().initWithAppKey(kRongIMKey, option: options)
        RCCoreClient.shared().logLevel = .log_Level_Verbose
        RCCoreClient.shared().addConnectionStatusChangeDelegate(self)
        RCCoreClient.shared().addReceiveMessageDelegate(self)
        //敏感词拦截
        RCCoreClient.shared().messageBlockDelegate = self
        RCCoreClient.shared().voiceMsgType = RCVoiceMessageType.highQuality
    }
    
    func loginIMWithIMToken(imToken:String) {
        guard let userData = LNUserManager.shared.userModel else { return  }
        
        RCCoreClient.shared().connect(withToken: imToken) { code in
            
        } success: { uid in
            print("msg--融云登陆成功-uid-\(String(describing: uid))")
        } error: { errorCode in
            print("msg--融云登陆失败-erroeCode-\(errorCode.rawValue)-uid-\(userData.id)")
        }

    }
}

extension LNMessageManager: RCConnectionStatusChangeDelegate {
    func onConnectionStatusChanged(_ status: RCConnectionStatus) {
        
    }

}

extension LNMessageManager: RCIMClientReceiveMessageDelegate {
    //收消息回调
    func onReceived(_ message: RCMessage, left nLeft: Int32, object: Any?, offline: Bool, hasPackage: Bool) {
        
    }
}

extension LNMessageManager: RCMessageBlockDelegate {
    func messageDidBlock(_ blockedMessageInfo: RCBlockedMessageInfo) {
        //消息拦截
    }
}
