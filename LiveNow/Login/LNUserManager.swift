//
//  LNUserManager.swift
//  LiveNow
//
//  Created by pp on 2025/8/13.
//

import UIKit
import JKSwiftExtension
import HandyJSON

class LNUserManager: NSObject {
    
    static let shared = LNUserManager()
    static let appInfoKey = "UserInfoKey"
    private override init() { }
    
    var userModel: LNUserModel?
    
    /// 获取用户信息
    func loadUserInfo() {
        if userModel != nil { return }
        let defaultStand = UserDefaults.standard
        
        let userJson = defaultStand.object(forKey: LNUserManager.appInfoKey) as? String
        
        if let u = LNUserModel.deserialize(from: userJson) {
            userModel = u
            loginThirdSDK()
        }
    }
    
    /// 存储用户
    func saveUserInfoToDisk(user: LNUserModel) {
        userModel = user
        loginThirdSDK()
        let defaultStand = UserDefaults.standard
        defaultStand.set(user.toJSONString(prettyPrint: false), forKey: LNUserManager.appInfoKey)
    }
    /// 用户登录、用户自动登录 登录第三方
    func loginThirdSDK() {
        //判断是否有用户信息
        guard let user = userModel else { return  }
//        LNMessageManager.shared.loginIMWithIMToken(imToken: user.token)
    }
    /// 是否登录
    func isLogin() -> Bool {
        if userModel != nil {
            return true
        }
        return false
    }
    
    /// 获取token
    func getToken() -> String {
        return (userModel?.token) ?? ""
    }
    
    /// 登录过期清除用户缓存
    func clearUserInfo() {
        userModel = nil
        let defaultStand = UserDefaults.standard
        defaultStand.removeObject(forKey: LNUserManager.appInfoKey)
    }


    /// 游客登录
    func visitorLogin(completeBlock: (([String: Any]) -> Void)?, failureBlock: ((_ error: NSError) -> Void)?) {
        let authId = LNCommons.lngetDeviceIdString()
        let dic = [
            "authId": authId,
            "type": 5
        ] as [String : Any]
        
        NetWorkRequest(LNApiCommon.authToken(par: dic)) { result in
            guard let data = result["data"] as? [String: Any] else {return}
            if let user = LNUserModel.deserialize(from: data) {
                // 保存用户信息
                LNUserManager.shared.saveUserInfoToDisk(user: user)
                // 切换root
                AppDelegate.shared.resetTabVC()
                
            }
            completeBlock?(result)
        } failure: { error in
            failureBlock?(error)
        }

//        NetWorkRequest(NetworkApiUser.visitorLogin(deviceId: deviceId)) { (response) in
//            guard let data = response["data"] as? [String: Any] else {return}
//            if let user = WOUser.deserialize(from: data) {
//                // 保存用户信息
//                WOUserManager.shared.saveUserInfoToDisk(user: user)
//
//                AppDelegate.shared.resetRootViewController()
//                
//                // 存储本次登录方式
//                let defaultStand = UserDefaults.standard
//                defaultStand.set("quickLogin", forKey: WOUserManager.lastLoginTypeKey)
//                
//                // Adjust打点-登录成功
//                WOStatisticsManager.adjustTrackEventWithName("login_success")
//                
//                // 谷歌分析- 注册/登录页面_快速登录按钮_登陆成功页面
//                WOStatisticsManager.analyticsLogEvent(name: "show_quickly_login")
//                
//            }
//            completeBlock?(response)
//        } failure: { (error) in
//            failureBlock?(error)
//        }
    }
    

}
